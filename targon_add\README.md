# Targon.com 批量注册工具

基于Node.js开发的Targon.com自动注册工具，支持批量注册账户并自动完成邮箱验证。

## 功能特性

- ✅ 自动生成随机邮箱和密码
- ✅ 自动注册Targon.com账户
- ✅ 自动获取验证邮件并完成邮箱验证
- ✅ 自动获取API Key (重要!)
- ✅ 支持批量注册多个账户
- ✅ 自动保存注册信息到JSON文件
- ✅ 完整的错误处理和重试机制
- ✅ 2FA验证功能 (自动/半自动)
- ✅ TOTP验证码自动生成

## 安装依赖

```bash
cd targon_add
npm install
```

## 使用方法

### 方法1: 直接运行主文件

```bash
# 注册1个账户
node targon-register.js

# 注册5个账户，间隔10秒
node targon-register.js 5 10000

# 注册10个账户，间隔15秒
node targon-register.js 10 15000
```

### 方法2: 使用npm脚本

```bash
# 注册1个账户
npm start

# 或者
npm run register
```

### 方法3: 使用示例文件

```bash
# 基础注册示例 (不含2FA)
node example.js basic

# 2FA注册示例
node example.js 2fa

# 批量2FA注册示例
node example.js batch2fa

# 运行所有示例
node example.js all
```

### 方法4: 2FA注册 (新功能)

```bash
# 在代码中使用
const register = new TargonRegister();

// 注册账户并启用自动2FA
const result = await register.registerWithTwoFA(true, true);

// 注册账户并启用手动2FA (显示二维码信息)
const result = await register.registerWithTwoFA(true, false);

// 批量注册并启用2FA
const results = await register.batchRegisterWithTwoFA(3, 15000, true, true);
```

## 配置说明

主要配置在 `config.js` 文件中：

- `email.domain`: 临时邮箱域名 (默认: umombiss.tk)
- `email.apiBase`: 临时邮箱API地址
- `password.length`: 密码长度 (默认: 8位)
- `register.defaultDelay`: 注册间隔时间 (默认: 10秒)

## 输出文件

注册成功的账户信息会保存在 `registered_accounts.json` 文件中，包含：

```json
[
  {
    "email": "<EMAIL>",
    "password": "xyz789ab",
    "verificationLink": "https://targon.com/email-verification/?token=...",
    "apiKey": "sn4_f6tlysvrbhqecja9j96imic6e1iw",
    "status": "verified",
    "loginStatus": true,
    "twoFactorEnabled": true,
    "twoFactorInfo": {
      "secret": "0d5bf0faa069a13e9cf4c4d731fcf1f3b6b51e1d",
      "manualCode": "BVN7B6VANGQT5HHUYTLTD7HR6O3LKHQ5",
      "uri": "otpauth://totp/https%3A%2F%2Ftargon.com:...",
      "manual": false
    },
    "registeredAt": "2025-01-16T10:30:00.000Z"
  }
]
```

## 注册流程

### 基础流程
1. 生成随机邮箱和密码
2. 调用Targon.com注册API
3. 等待验证邮件到达临时邮箱
4. 提取验证链接并访问完成验证
5. 登录账户获取认证状态
6. 获取API Key (重要!)
7. 保存账户信息到JSON文件

### 完整流程 (含2FA)
1-7. 执行基础流程 (含登录)
8. 创建2FA URI和Secret
9. 生成TOTP验证码 (自动模式)
10. 验证2FA代码完成设置
11. 保存完整账户信息 (含2FA信息)

## 注意事项

- 确保临时邮箱服务 (umombiss.tk) 正常运行
- 建议设置合理的注册间隔避免被限制
- API Key是最重要的信息，请妥善保存
- 2FA功能支持自动和半自动两种模式
- 自动2FA会直接生成并验证TOTP码
- 半自动2FA会显示二维码信息供手动扫描
- 注册失败的账户也会记录在输出文件中

## 文件结构

```
targon_add/
├── targon-register.js    # 主注册类
├── twofa-handler.js     # 2FA处理模块
├── example.js           # 使用示例 (支持多种模式)
├── config.js           # 配置文件
├── package.json        # 项目配置
├── README.md          # 说明文档
└── registered_accounts.json  # 输出文件(运行后生成)
```

## 技术栈

- Node.js
- Axios (HTTP请求)
- 临时邮箱API (umombiss.tk)
- Targon.com tRPC API
- TOTP算法 (RFC 6238)
- HMAC-SHA1加密
- Base32编码解码

## 开发说明

基于实际抓包分析的Targon.com API接口：
- 邮箱检查: `/api/trpc/account.checkEmail,account.check2FA`
- 账户创建: `/api/trpc/account.createAccount`
- 账户登录: `/api/trpc/account.signIn` (关键!)
- 用户信息: `/api/trpc/account.getUserInterest,account.getUserSubscription,account.check2FA,account.getTaoPrice,account.getAlphaPrice,keys.getApiKeys,notification.getNotifications`
- 2FA创建: `/api/trpc/account.createTwoFactorURI`
- 2FA验证: `/api/trpc/account.verify2FA`
- 使用tRPC框架的batch请求格式
- 需要登录状态才能获取API Key

## 2FA技术实现

- 自动解析2FA URI和Secret
- 基于RFC 6238标准生成TOTP验证码
- 支持30秒时间窗口和6位数字验证码
- 自动Base32解码和HMAC-SHA1计算
- 支持自动验证和手动验证两种模式
