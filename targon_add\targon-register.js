const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const TwoFAHandler = require('./twofa-handler');

class TargonRegister {
    constructor() {
        this.baseUrl = 'https://targon.com';
        this.emailDomain = 'umombiss.tk';
        this.emailApiBase = 'http://umombiss.tk'; // 临时邮箱API地址
        this.registeredAccounts = [];
        this.outputFile = 'registered_accounts.json';
        this.cookies = ''; // 存储Cookie
        this.isLoggedIn = false; // 登录状态

        // 请求头配置
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        };

        // 初始化2FA处理器
        this.twoFAHandler = new TwoFAHandler(this.baseUrl, this.headers);
    }

    // 生成随机字符串
    generateRandomString(length, chars = 'abcdefghijklmnopqrstuvwxyz0123456789') {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // 生成随机邮箱
    generateRandomEmail() {
        const prefix = this.generateRandomString(8);
        return `${prefix}@${this.emailDomain}`;
    }

    // 生成随机密码
    generateRandomPassword() {
        return this.generateRandomString(8);
    }

    // 检查邮箱是否存在
    async checkEmail(email) {
        try {
            const params = {
                "0": {
                    "json": {
                        "email": email
                    }
                },
                "1": {
                    "json": {
                        "email": email
                    }
                }
            };

            const response = await axios.get(`${this.baseUrl}/api/trpc/account.checkEmail,account.check2FA`, {
                params: {
                    batch: 1,
                    input: JSON.stringify(params)
                },
                headers: this.headers
            });

            console.log(`邮箱检查结果: ${email}`, response.data);
            return response.data;
        } catch (error) {
            console.error('检查邮箱失败:', error.message);
            throw error;
        }
    }

    // 创建账户
    async createAccount(email, password) {
        try {
            const data = {
                "0": {
                    "json": {
                        "email": email,
                        "password": password,
                        "password2": password
                    }
                }
            };

            const response = await axios.post(`${this.baseUrl}/api/trpc/account.createAccount?batch=1`, data, {
                headers: {
                    ...this.headers,
                    'Content-Type': 'application/json'
                }
            });

            console.log(`账户创建结果: ${email}`, response.data);
            return response.data;
        } catch (error) {
            console.error('创建账户失败:', error.message);
            throw error;
        }
    }

    // 获取邮件列表
    async getEmails(email) {
        try {
            const encodedEmail = encodeURIComponent(email);
            const response = await axios.get(`${this.emailApiBase}/api/mails/${encodedEmail}`);
            return response.data.mails || [];
        } catch (error) {
            console.error('获取邮件失败:', error.message);
            return [];
        }
    }

    // 等待验证邮件
    async waitForVerificationEmail(email, maxAttempts = 30, interval = 5000) {
        console.log(`等待验证邮件: ${email}`);
        
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                const emails = await this.getEmails(email);
                
                for (const mail of emails) {
                    if (mail.subject && mail.subject.toLowerCase().includes('verification') || 
                        mail.text && mail.text.includes('email-verification')) {
                        
                        // 提取验证链接
                        const verificationLink = this.extractVerificationLink(mail.text || mail.html);
                        if (verificationLink) {
                            console.log(`找到验证邮件: ${email}, 链接: ${verificationLink}`);
                            return verificationLink;
                        }
                    }
                }
                
                console.log(`第 ${attempt} 次检查，未找到验证邮件，等待 ${interval/1000} 秒...`);
                await this.sleep(interval);
            } catch (error) {
                console.error(`检查邮件失败 (第${attempt}次):`, error.message);
                await this.sleep(interval);
            }
        }
        
        throw new Error(`等待验证邮件超时: ${email}`);
    }

    // 提取验证链接
    extractVerificationLink(content) {
        const regex = /https:\/\/targon\.com\/email-verification\/\?token=[a-zA-Z0-9]+/g;
        const match = content.match(regex);
        return match ? match[0] : null;
    }

    // 访问验证链接
    async verifyEmail(verificationLink) {
        try {
            const response = await axios.get(verificationLink, {
                headers: this.headers,
                maxRedirects: 5
            });

            console.log(`邮箱验证成功: ${verificationLink}`);
            return true;
        } catch (error) {
            console.error('邮箱验证失败:', error.message);
            return false;
        }
    }

    // 获取用户信息和API Key
    async getUserInfo() {
        try {
            const params = {
                "0": {"json": null, "meta": {"values": ["undefined"]}},
                "1": {"json": null, "meta": {"values": ["undefined"]}},
                "2": {"json": null, "meta": {"values": ["undefined"]}},
                "3": {"json": null, "meta": {"values": ["undefined"]}},
                "4": {"json": null, "meta": {"values": ["undefined"]}},
                "5": {"json": null, "meta": {"values": ["undefined"]}},
                "6": {"json": null, "meta": {"values": ["undefined"]}}
            };

            const response = await axios.get(`${this.baseUrl}/api/trpc/account.getUserInterest,account.getUserSubscription,account.check2FA,account.getTaoPrice,account.getAlphaPrice,keys.getApiKeys,notification.getNotifications`, {
                params: {
                    batch: 1,
                    input: JSON.stringify(params)
                },
                headers: this.headers
            });

            console.log('用户信息获取成功');

            // 提取API Key
            const apiKey = this.extractApiKey(response.data);
            return {
                userInfo: response.data,
                apiKey: apiKey
            };
        } catch (error) {
            console.error('获取用户信息失败:', error.message);
            return null;
        }
    }

    // 从响应中提取API Key
    extractApiKey(responseData) {
        try {
            // API Key通常在keys.getApiKeys的响应中
            if (responseData && Array.isArray(responseData)) {
                for (const item of responseData) {
                    if (item.result && item.result.data && item.result.data.json) {
                        const data = item.result.data.json;
                        // 查找API Key字段
                        if (data.apiKeys && Array.isArray(data.apiKeys) && data.apiKeys.length > 0) {
                            return data.apiKeys[0].key || data.apiKeys[0].id || data.apiKeys[0];
                        }
                        // 其他可能的API Key字段
                        if (data.key) return data.key;
                        if (data.apiKey) return data.apiKey;
                        if (data.token) return data.token;
                    }
                }
            }
            return null;
        } catch (error) {
            console.error('提取API Key失败:', error.message);
            return null;
        }
    }

    // 睡眠函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 保存账户信息到JSON文件
    async saveAccountToFile(accountInfo) {
        try {
            let existingData = [];
            
            // 尝试读取现有文件
            try {
                const fileContent = await fs.readFile(this.outputFile, 'utf8');
                existingData = JSON.parse(fileContent);
            } catch (error) {
                // 文件不存在或为空，使用空数组
                console.log('创建新的账户文件');
            }
            
            // 添加新账户信息
            existingData.push({
                ...accountInfo,
                registeredAt: new Date().toISOString()
            });
            
            // 写入文件
            await fs.writeFile(this.outputFile, JSON.stringify(existingData, null, 2), 'utf8');
            console.log(`账户信息已保存到: ${this.outputFile}`);
            
        } catch (error) {
            console.error('保存账户信息失败:', error.message);
        }
    }

    // 单个账户注册流程 (不包含2FA)
    async registerSingleAccount() {
        const email = this.generateRandomEmail();
        const password = this.generateRandomPassword();

        console.log(`\n开始注册账户: ${email}`);

        try {
            // 1. 检查邮箱
            await this.checkEmail(email);

            // 2. 创建账户
            await this.createAccount(email, password);

            // 3. 等待验证邮件
            const verificationLink = await this.waitForVerificationEmail(email);

            // 4. 验证邮箱
            const verified = await this.verifyEmail(verificationLink);

            if (verified) {
                // 等待几秒让系统处理验证状态
                console.log('等待系统处理验证状态...');
                await this.sleep(3000);

                // 5. 获取用户信息和API Key
                const userInfoResult = await this.getUserInfo();
                let apiKey = null;

                if (userInfoResult && userInfoResult.apiKey) {
                    apiKey = userInfoResult.apiKey;
                    console.log(`✅ API Key获取成功: ${apiKey}`);
                } else {
                    console.log('⚠️ API Key获取失败，可能需要手动获取');
                }

                const accountInfo = {
                    email: email,
                    password: password,
                    verificationLink: verificationLink,
                    apiKey: apiKey,
                    status: 'verified',
                    // TODO: 2FA验证功能待实现
                    twoFactorEnabled: false
                };

                // 6. 保存账户信息
                await this.saveAccountToFile(accountInfo);
                this.registeredAccounts.push(accountInfo);

                console.log(`✅ 账户注册成功: ${email}`);
                return accountInfo;
            } else {
                throw new Error('邮箱验证失败');
            }

        } catch (error) {
            console.error(`❌ 账户注册失败: ${email} - ${error.message}`);

            // 保存失败的账户信息
            const failedAccountInfo = {
                email: email,
                password: password,
                status: 'failed',
                error: error.message,
                apiKey: null,
                twoFactorEnabled: false
            };

            await this.saveAccountToFile(failedAccountInfo);
            return null;
        }
    }

    // 批量注册
    async batchRegister(count = 1, delay = 10000) {
        console.log(`开始批量注册 ${count} 个账户，间隔 ${delay/1000} 秒`);
        
        const results = [];
        
        for (let i = 1; i <= count; i++) {
            console.log(`\n=== 注册第 ${i}/${count} 个账户 ===`);
            
            const result = await this.registerSingleAccount();
            results.push(result);
            
            // 如果不是最后一个账户，等待指定时间
            if (i < count) {
                console.log(`等待 ${delay/1000} 秒后继续下一个账户...`);
                await this.sleep(delay);
            }
        }
        
        const successCount = results.filter(r => r !== null).length;
        console.log(`\n=== 批量注册完成 ===`);
        console.log(`成功: ${successCount}/${count}`);
        console.log(`失败: ${count - successCount}/${count}`);
        
        return results;
    }

    // 完整注册流程 (包含2FA)
    async registerWithTwoFA(enable2FA = false, auto2FA = false) {
        const email = this.generateRandomEmail();
        const password = this.generateRandomPassword();

        console.log(`\n开始完整注册流程: ${email}`);
        console.log(`2FA设置: ${enable2FA ? '启用' : '禁用'}`);
        console.log(`2FA模式: ${auto2FA ? '自动' : '手动'}`);

        try {
            // 1-4. 基础注册流程
            await this.checkEmail(email);
            await this.createAccount(email, password);
            const verificationLink = await this.waitForVerificationEmail(email);
            const verified = await this.verifyEmail(verificationLink);

            if (!verified) {
                throw new Error('邮箱验证失败');
            }

            // 等待系统处理
            await this.sleep(3000);

            // 5. 获取API Key
            const userInfoResult = await this.getUserInfo();
            let apiKey = null;

            if (userInfoResult && userInfoResult.apiKey) {
                apiKey = userInfoResult.apiKey;
                console.log(`✅ API Key获取成功: ${apiKey}`);
            } else {
                console.log('⚠️ API Key获取失败，可能需要手动获取');
            }

            let twoFAResult = null;

            // 6. 2FA设置 (可选)
            if (enable2FA) {
                console.log('\n开始2FA设置...');

                if (auto2FA) {
                    // 自动2FA设置
                    twoFAResult = await this.twoFAHandler.setup2FA();
                } else {
                    // 半自动2FA设置
                    twoFAResult = await this.twoFAHandler.setup2FAManual();
                }

                if (twoFAResult && twoFAResult.success) {
                    console.log('✅ 2FA设置成功');
                } else {
                    console.log('⚠️ 2FA设置失败:', twoFAResult?.error || '未知错误');
                }
            }

            const accountInfo = {
                email: email,
                password: password,
                verificationLink: verificationLink,
                apiKey: apiKey,
                status: 'verified',
                twoFactorEnabled: enable2FA && twoFAResult?.success,
                twoFactorInfo: twoFAResult?.success ? {
                    secret: twoFAResult.twoFAInfo?.secret,
                    manualCode: twoFAResult.twoFAInfo?.manualCode,
                    uri: twoFAResult.twoFAInfo?.uri,
                    manual: twoFAResult.manual || false
                } : null
            };

            // 7. 保存账户信息
            await this.saveAccountToFile(accountInfo);
            this.registeredAccounts.push(accountInfo);

            console.log(`✅ 完整注册流程成功: ${email}`);
            return accountInfo;

        } catch (error) {
            console.error(`❌ 完整注册流程失败: ${email} - ${error.message}`);

            const failedAccountInfo = {
                email: email,
                password: password,
                status: 'failed',
                error: error.message,
                apiKey: null,
                twoFactorEnabled: false,
                twoFactorInfo: null
            };

            await this.saveAccountToFile(failedAccountInfo);
            return null;
        }
    }

    // 批量注册 (包含2FA选项)
    async batchRegisterWithTwoFA(count = 1, delay = 10000, enable2FA = false, auto2FA = false) {
        console.log(`开始批量注册 ${count} 个账户，间隔 ${delay/1000} 秒`);
        console.log(`2FA设置: ${enable2FA ? '启用' : '禁用'}`);
        console.log(`2FA模式: ${auto2FA ? '自动' : '手动'}`);

        const results = [];

        for (let i = 1; i <= count; i++) {
            console.log(`\n=== 注册第 ${i}/${count} 个账户 ===`);

            const result = await this.registerWithTwoFA(enable2FA, auto2FA);
            results.push(result);

            if (i < count) {
                console.log(`等待 ${delay/1000} 秒后继续下一个账户...`);
                await this.sleep(delay);
            }
        }

        const successCount = results.filter(r => r !== null).length;
        const twoFACount = results.filter(r => r && r.twoFactorEnabled).length;

        console.log(`\n=== 批量注册完成 ===`);
        console.log(`成功: ${successCount}/${count}`);
        console.log(`失败: ${count - successCount}/${count}`);
        console.log(`2FA启用: ${twoFACount}/${successCount}`);

        return results;
    }
}

// 导出类
module.exports = TargonRegister;

// 如果直接运行此文件
if (require.main === module) {
    const register = new TargonRegister();
    
    // 从命令行参数获取注册数量
    const count = parseInt(process.argv[2]) || 1;
    const delay = parseInt(process.argv[3]) || 10000;
    
    register.batchRegister(count, delay)
        .then(() => {
            console.log('批量注册任务完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('批量注册任务失败:', error);
            process.exit(1);
        });
}
