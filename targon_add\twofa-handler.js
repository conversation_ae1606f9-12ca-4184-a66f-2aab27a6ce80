const axios = require('axios');
const crypto = require('crypto');

class TwoFAHandler {
    constructor(baseUrl, headers) {
        this.baseUrl = baseUrl;
        this.headers = headers;
    }

    // 创建2FA URI
    async create2FAURI() {
        try {
            const params = {
                "0": {
                    "json": null,
                    "meta": {
                        "values": ["undefined"]
                    }
                }
            };

            const response = await axios.get(`${this.baseUrl}/api/trpc/account.createTwoFactorURI`, {
                params: {
                    batch: 1,
                    input: JSON.stringify(params)
                },
                headers: this.headers
            });

            console.log('2FA URI创建成功');
            return this.extract2FAInfo(response.data);
        } catch (error) {
            console.error('创建2FA URI失败:', error.message);
            return null;
        }
    }

    // 从响应中提取2FA信息
    extract2FAInfo(responseData) {
        try {
            if (responseData && Array.isArray(responseData)) {
                for (const item of responseData) {
                    if (item.result && item.result.data && item.result.data.json) {
                        const data = item.result.data.json;
                        return {
                            uri: data.uri,
                            secret: data.twoFactorSecret,
                            manualCode: data.manualCode
                        };
                    }
                }
            }
            return null;
        } catch (error) {
            console.error('提取2FA信息失败:', error.message);
            return null;
        }
    }

    // 生成TOTP验证码 (基于RFC 6238)
    generateTOTP(secret, timeStep = 30, digits = 6) {
        try {
            // 将base32 secret转换为buffer
            const secretBuffer = this.base32ToBuffer(secret);
            
            // 获取当前时间步长
            const timeCounter = Math.floor(Date.now() / 1000 / timeStep);
            
            // 创建时间计数器的8字节buffer
            const timeBuffer = Buffer.alloc(8);
            timeBuffer.writeUInt32BE(Math.floor(timeCounter / 0x100000000), 0);
            timeBuffer.writeUInt32BE(timeCounter & 0xffffffff, 4);
            
            // 使用HMAC-SHA1生成hash
            const hmac = crypto.createHmac('sha1', secretBuffer);
            hmac.update(timeBuffer);
            const hash = hmac.digest();
            
            // 动态截取
            const offset = hash[hash.length - 1] & 0x0f;
            const code = ((hash[offset] & 0x7f) << 24) |
                        ((hash[offset + 1] & 0xff) << 16) |
                        ((hash[offset + 2] & 0xff) << 8) |
                        (hash[offset + 3] & 0xff);
            
            // 生成指定位数的验证码
            const otp = (code % Math.pow(10, digits)).toString().padStart(digits, '0');
            return otp;
        } catch (error) {
            console.error('生成TOTP验证码失败:', error.message);
            return null;
        }
    }

    // Base32解码 (简化版本)
    base32ToBuffer(base32) {
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        let bits = '';
        
        // 移除填充和空格
        base32 = base32.replace(/=+$/, '').replace(/\s/g, '').toUpperCase();
        
        // 转换为二进制字符串
        for (let i = 0; i < base32.length; i++) {
            const char = base32[i];
            const index = alphabet.indexOf(char);
            if (index === -1) {
                throw new Error(`Invalid base32 character: ${char}`);
            }
            bits += index.toString(2).padStart(5, '0');
        }
        
        // 转换为buffer
        const bytes = [];
        for (let i = 0; i < bits.length; i += 8) {
            const byte = bits.substr(i, 8);
            if (byte.length === 8) {
                bytes.push(parseInt(byte, 2));
            }
        }
        
        return Buffer.from(bytes);
    }

    // 验证2FA代码
    async verify2FA(code) {
        try {
            const data = {
                "0": {
                    "json": {
                        "code": code
                    }
                }
            };

            const response = await axios.post(`${this.baseUrl}/api/trpc/account.verify2FA?batch=1`, data, {
                headers: {
                    ...this.headers,
                    'Content-Type': 'application/json'
                }
            });

            console.log('2FA验证结果:', response.data);
            return response.data;
        } catch (error) {
            console.error('2FA验证失败:', error.message);
            return null;
        }
    }

    // 完整的2FA设置流程
    async setup2FA() {
        console.log('开始2FA设置流程...');
        
        try {
            // 1. 创建2FA URI
            const twoFAInfo = await this.create2FAURI();
            if (!twoFAInfo) {
                throw new Error('创建2FA URI失败');
            }
            
            console.log('2FA信息:');
            console.log('- URI:', twoFAInfo.uri);
            console.log('- Secret:', twoFAInfo.secret);
            console.log('- Manual Code:', twoFAInfo.manualCode);
            
            // 2. 生成TOTP验证码
            const totpCode = this.generateTOTP(twoFAInfo.manualCode);
            if (!totpCode) {
                throw new Error('生成TOTP验证码失败');
            }
            
            console.log('生成的TOTP验证码:', totpCode);
            
            // 3. 验证2FA代码
            const verifyResult = await this.verify2FA(totpCode);
            if (verifyResult) {
                console.log('✅ 2FA设置成功');
                return {
                    success: true,
                    twoFAInfo: twoFAInfo,
                    totpCode: totpCode
                };
            } else {
                throw new Error('2FA验证失败');
            }
            
        } catch (error) {
            console.error('❌ 2FA设置失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 半自动化2FA设置 (显示信息供手动操作)
    async setup2FAManual() {
        console.log('开始半自动化2FA设置...');
        
        try {
            // 1. 创建2FA URI
            const twoFAInfo = await this.create2FAURI();
            if (!twoFAInfo) {
                throw new Error('创建2FA URI失败');
            }
            
            console.log('\n=== 2FA设置信息 ===');
            console.log('请使用Google Authenticator或其他TOTP应用扫描以下信息:');
            console.log('URI:', twoFAInfo.uri);
            console.log('手动输入代码:', twoFAInfo.manualCode);
            console.log('Secret:', twoFAInfo.secret);
            
            // 生成当前时间的TOTP码供参考
            const currentTOTP = this.generateTOTP(twoFAInfo.manualCode);
            console.log('当前TOTP验证码 (供参考):', currentTOTP);
            console.log('注意: TOTP验证码每30秒更新一次');
            
            return {
                success: true,
                twoFAInfo: twoFAInfo,
                currentTOTP: currentTOTP,
                manual: true
            };
            
        } catch (error) {
            console.error('❌ 半自动化2FA设置失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = TwoFAHandler;
