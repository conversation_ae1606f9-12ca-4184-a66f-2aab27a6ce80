<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Targon 批量注册工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .control-row {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .input-group label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9em;
        }

        .input-group input, .input-group select {
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .status-panel {
            background: #fff;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .status-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .status-card h3 {
            font-size: 2em;
            margin-bottom: 5px;
        }

        .status-card p {
            opacity: 0.9;
        }

        .accounts-section {
            background: #fff;
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .accounts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .accounts-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .accounts-table th,
        .accounts-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .accounts-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .accounts-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .api-key {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            display: none;
        }

        .log-container.show {
            display: block;
        }

        @media (max-width: 768px) {
            .control-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .accounts-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Targon 批量注册工具</h1>
            <p>高效批量注册 Targon 账户，自动启用2FA获取奖励</p>
        </div>

        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="control-row">
                    <div class="input-group">
                        <label for="accountCount">注册数量</label>
                        <input type="number" id="accountCount" min="1" max="50" value="5">
                    </div>
                    <div class="input-group">
                        <label for="concurrency">并发数</label>
                        <input type="number" id="concurrency" min="1" max="10" value="3">
                    </div>
                    <div class="input-group">
                        <label for="mode">注册模式</label>
                        <select id="mode">
                            <option value="parallel">并行注册</option>
                            <option value="serial">串行注册</option>
                        </select>
                    </div>
                </div>
                <div class="control-row">
                    <button class="btn btn-primary" id="startBtn">
                        <span>🚀</span> 开始注册
                    </button>
                    <button class="btn btn-secondary" id="stopBtn" disabled>
                        <span>⏹️</span> 停止注册
                    </button>
                    <button class="btn btn-success" id="exportBtn">
                        <span>📥</span> 导出API Keys
                    </button>
                    <button class="btn btn-secondary" id="clearBtn">
                        <span>🗑️</span> 清空记录
                    </button>
                </div>
            </div>

            <!-- 状态面板 -->
            <div class="status-panel">
                <div class="status-grid">
                    <div class="status-card">
                        <h3 id="totalCount">0</h3>
                        <p>总计账户</p>
                    </div>
                    <div class="status-card">
                        <h3 id="successCount">0</h3>
                        <p>成功注册</p>
                    </div>
                    <div class="status-card">
                        <h3 id="failedCount">0</h3>
                        <p>注册失败</p>
                    </div>
                    <div class="status-card">
                        <h3 id="progressPercent">0%</h3>
                        <p>完成进度</p>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="log-container" id="logContainer"></div>
            </div>

            <!-- 账户列表 -->
            <div class="accounts-section">
                <div class="accounts-header">
                    <h2>📋 注册账户列表</h2>
                    <div>
                        <button class="btn btn-secondary" id="toggleLogBtn">
                            <span>📜</span> 显示日志
                        </button>
                        <button class="btn btn-secondary" id="refreshBtn">
                            <span>🔄</span> 刷新列表
                        </button>
                    </div>
                </div>
                <table class="accounts-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>邮箱</th>
                            <th>API Key</th>
                            <th>2FA</th>
                            <th>余额</th>
                            <th>状态</th>
                            <th>注册时间</th>
                        </tr>
                    </thead>
                    <tbody id="accountsTableBody">
                        <!-- 动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="web-interface.js"></script>
</body>
</html>
