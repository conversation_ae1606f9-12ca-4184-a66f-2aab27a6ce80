{"name": "targon-register", "version": "1.0.0", "description": "Targon.com批量注册工具", "main": "targon-register.js", "scripts": {"start": "node targon-register.js", "register": "node targon-register.js", "web": "node web-server.js", "install-deps": "npm install"}, "dependencies": {"axios": "^1.6.0", "otplib": "^12.0.1", "express": "^4.18.2"}, "keywords": ["targon", "register", "automation", "batch"], "author": "", "license": "MIT", "engines": {"node": ">=14.0.0"}}