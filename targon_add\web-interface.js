class TargonWebInterface {
    constructor() {
        this.isRegistering = false;
        this.accounts = [];
        this.currentProgress = 0;
        this.totalTasks = 0;
        
        this.initializeElements();
        this.bindEvents();
        this.loadAccounts();
    }

    initializeElements() {
        // 控制元素
        this.accountCountInput = document.getElementById('accountCount');
        this.concurrencyInput = document.getElementById('concurrency');
        this.modeSelect = document.getElementById('mode');
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.exportBtn = document.getElementById('exportBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.refreshBtn = document.getElementById('refreshBtn');
        this.toggleLogBtn = document.getElementById('toggleLogBtn');

        // 状态元素
        this.totalCountEl = document.getElementById('totalCount');
        this.successCountEl = document.getElementById('successCount');
        this.failedCountEl = document.getElementById('failedCount');
        this.progressPercentEl = document.getElementById('progressPercent');
        this.progressFillEl = document.getElementById('progressFill');
        this.logContainer = document.getElementById('logContainer');

        // 表格元素
        this.accountsTableBody = document.getElementById('accountsTableBody');
    }

    bindEvents() {
        this.startBtn.addEventListener('click', () => this.startRegistration());
        this.stopBtn.addEventListener('click', () => this.stopRegistration());
        this.exportBtn.addEventListener('click', () => this.exportApiKeys());
        this.clearBtn.addEventListener('click', () => this.clearAccounts());
        this.refreshBtn.addEventListener('click', () => this.loadAccounts());
        this.toggleLogBtn.addEventListener('click', () => this.toggleLog());
    }

    async startRegistration() {
        if (this.isRegistering) return;

        const count = parseInt(this.accountCountInput.value);
        const concurrency = parseInt(this.concurrencyInput.value);
        const mode = this.modeSelect.value;

        if (count < 1 || count > 50) {
            alert('注册数量必须在1-50之间');
            return;
        }

        if (concurrency < 1 || concurrency > 10) {
            alert('并发数必须在1-10之间');
            return;
        }

        this.isRegistering = true;
        this.currentProgress = 0;
        this.totalTasks = count;
        
        this.startBtn.disabled = true;
        this.stopBtn.disabled = false;
        
        this.log(`🚀 开始${mode === 'parallel' ? '并行' : '串行'}注册 ${count} 个账户`);
        this.log(`📊 并发数: ${concurrency}`);

        try {
            const response = await fetch('/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    count: count,
                    concurrency: concurrency,
                    mode: mode
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n').filter(line => line.trim());

                for (const line of lines) {
                    try {
                        const data = JSON.parse(line);
                        this.handleRegistrationUpdate(data);
                    } catch (e) {
                        // 忽略非JSON行
                        if (line.trim()) {
                            this.log(line);
                        }
                    }
                }
            }

        } catch (error) {
            this.log(`❌ 注册过程出错: ${error.message}`);
        } finally {
            this.isRegistering = false;
            this.startBtn.disabled = false;
            this.stopBtn.disabled = true;
            this.log('✅ 注册任务完成');
            this.loadAccounts();
        }
    }

    handleRegistrationUpdate(data) {
        switch (data.type) {
            case 'progress':
                this.updateProgress(data.current, data.total);
                break;
            case 'account':
                this.addAccountToTable(data.account);
                break;
            case 'log':
                this.log(data.message);
                break;
            case 'complete':
                this.log(`🎉 注册完成! 成功: ${data.success}/${data.total}, 失败: ${data.failed}/${data.total}`);
                break;
        }
    }

    updateProgress(current, total) {
        this.currentProgress = current;
        this.totalTasks = total;
        
        const percent = Math.round((current / total) * 100);
        this.progressPercentEl.textContent = `${percent}%`;
        this.progressFillEl.style.width = `${percent}%`;
    }

    addAccountToTable(account) {
        this.accounts.push(account);
        this.updateStats();
        this.renderAccountsTable();
    }

    updateStats() {
        const total = this.accounts.length;
        const success = this.accounts.filter(acc => acc.status === 'verified').length;
        const failed = total - success;

        this.totalCountEl.textContent = total;
        this.successCountEl.textContent = success;
        this.failedCountEl.textContent = failed;
    }

    renderAccountsTable() {
        this.accountsTableBody.innerHTML = '';
        
        this.accounts.forEach((account, index) => {
            const row = document.createElement('tr');
            
            const statusBadge = account.status === 'verified' 
                ? '<span class="status-badge status-success">成功</span>'
                : '<span class="status-badge status-failed">失败</span>';
            
            const apiKey = account.apiKey 
                ? `<span class="api-key">${account.apiKey.slice(-8)}</span>`
                : '<span class="api-key">N/A</span>';
            
            const twoFA = account.twoFactorEnabled ? '✅' : '❌';
            
            const balance = account.creditBalance 
                ? `$${(account.creditBalance.totalCredits / ********).toFixed(2)}`
                : '$0.00';
            
            const registeredAt = account.registeredAt 
                ? new Date(account.registeredAt).toLocaleString('zh-CN')
                : '未知';

            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${account.email}</td>
                <td>${apiKey}</td>
                <td>${twoFA}</td>
                <td>${balance}</td>
                <td>${statusBadge}</td>
                <td>${registeredAt}</td>
            `;
            
            this.accountsTableBody.appendChild(row);
        });
    }

    async loadAccounts() {
        try {
            const response = await fetch('/api/accounts');
            if (response.ok) {
                this.accounts = await response.json();
                this.updateStats();
                this.renderAccountsTable();
            }
        } catch (error) {
            this.log(`❌ 加载账户列表失败: ${error.message}`);
        }
    }

    async exportApiKeys() {
        const successAccounts = this.accounts.filter(acc => acc.status === 'verified' && acc.apiKey);
        
        if (successAccounts.length === 0) {
            alert('没有可导出的API Key');
            return;
        }

        const apiKeys = successAccounts.map(acc => acc.apiKey).join('\n');
        
        const blob = new Blob([apiKeys], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `targon_api_keys_${new Date().toISOString().slice(0, 10)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
        this.log(`📥 已导出 ${successAccounts.length} 个API Key`);
    }

    async clearAccounts() {
        if (!confirm('确定要清空所有账户记录吗？此操作不可恢复！')) {
            return;
        }

        try {
            const response = await fetch('/api/accounts', { method: 'DELETE' });
            if (response.ok) {
                this.accounts = [];
                this.updateStats();
                this.renderAccountsTable();
                this.log('🗑️ 已清空所有账户记录');
            }
        } catch (error) {
            this.log(`❌ 清空记录失败: ${error.message}`);
        }
    }

    stopRegistration() {
        if (!this.isRegistering) return;
        
        // 这里可以发送停止请求到后端
        fetch('/api/stop', { method: 'POST' })
            .then(() => {
                this.log('⏹️ 已请求停止注册');
            })
            .catch(error => {
                this.log(`❌ 停止请求失败: ${error.message}`);
            });
    }

    toggleLog() {
        const isVisible = this.logContainer.classList.contains('show');
        if (isVisible) {
            this.logContainer.classList.remove('show');
            this.toggleLogBtn.innerHTML = '<span>📜</span> 显示日志';
        } else {
            this.logContainer.classList.add('show');
            this.toggleLogBtn.innerHTML = '<span>📜</span> 隐藏日志';
        }
    }

    log(message) {
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        const logLine = `[${timestamp}] ${message}`;
        
        this.logContainer.innerHTML += logLine + '\n';
        this.logContainer.scrollTop = this.logContainer.scrollHeight;
        
        console.log(logLine);
    }
}

// 初始化界面
document.addEventListener('DOMContentLoaded', () => {
    new TargonWebInterface();
});
