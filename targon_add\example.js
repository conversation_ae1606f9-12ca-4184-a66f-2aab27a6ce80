const TargonRegister = require('./targon-register');

// 基础注册示例
async function basicExample() {
    const register = new TargonRegister();

    console.log('=== 基础注册示例 ===\n');

    try {
        // 示例1: 注册单个账户 (不含2FA)
        console.log('示例1: 注册单个账户 (不含2FA)');
        const singleResult = await register.registerSingleAccount();

        if (singleResult) {
            console.log('单个账户注册成功:');
            console.log(`- 邮箱: ${singleResult.email}`);
            console.log(`- 密码: ${singleResult.password}`);
            console.log(`- API Key: ${singleResult.apiKey || '未获取到'}`);
        }

        console.log('\n等待10秒后继续批量注册...\n');
        await register.sleep(10000);

        // 示例2: 批量注册2个账户，间隔15秒
        console.log('示例2: 批量注册2个账户 (不含2FA)');
        const batchResults = await register.batchRegister(2, 15000);

        // 显示成功的账户
        const successfulAccounts = batchResults.filter(result => result !== null);
        console.log(`\n成功注册的账户 (${successfulAccounts.length}个):`);
        successfulAccounts.forEach((account, index) => {
            console.log(`${index + 1}. 邮箱: ${account.email}, 密码: ${account.password}, API Key: ${account.apiKey || '未获取到'}`);
        });

    } catch (error) {
        console.error('基础注册过程中发生错误:', error);
    }
}

// 2FA注册示例
async function twoFAExample() {
    const register = new TargonRegister();

    console.log('\n=== 2FA注册示例 ===\n');

    try {
        // 示例3: 注册单个账户 (含自动2FA)
        console.log('示例3: 注册单个账户 (含自动2FA)');
        const autoTwoFAResult = await register.registerWithTwoFA(true, true);

        if (autoTwoFAResult) {
            console.log('自动2FA账户注册成功:');
            console.log(`- 邮箱: ${autoTwoFAResult.email}`);
            console.log(`- 密码: ${autoTwoFAResult.password}`);
            console.log(`- API Key: ${autoTwoFAResult.apiKey || '未获取到'}`);
            console.log(`- 2FA启用: ${autoTwoFAResult.twoFactorEnabled}`);
            if (autoTwoFAResult.twoFactorInfo) {
                console.log(`- 2FA Secret: ${autoTwoFAResult.twoFactorInfo.secret}`);
                console.log(`- 2FA Manual Code: ${autoTwoFAResult.twoFactorInfo.manualCode}`);
            }
        }

        console.log('\n等待15秒后继续...\n');
        await register.sleep(15000);

        // 示例4: 注册单个账户 (含手动2FA)
        console.log('示例4: 注册单个账户 (含手动2FA)');
        const manualTwoFAResult = await register.registerWithTwoFA(true, false);

        if (manualTwoFAResult) {
            console.log('手动2FA账户注册成功:');
            console.log(`- 邮箱: ${manualTwoFAResult.email}`);
            console.log(`- 密码: ${manualTwoFAResult.password}`);
            console.log(`- API Key: ${manualTwoFAResult.apiKey || '未获取到'}`);
            console.log(`- 2FA启用: ${manualTwoFAResult.twoFactorEnabled}`);
            if (manualTwoFAResult.twoFactorInfo) {
                console.log('- 请使用Google Authenticator扫描上面显示的2FA信息');
            }
        }

    } catch (error) {
        console.error('2FA注册过程中发生错误:', error);
    }
}

// 批量2FA注册示例
async function batchTwoFAExample() {
    const register = new TargonRegister();

    console.log('\n=== 批量2FA注册示例 ===\n');

    try {
        // 示例5: 批量注册2个账户 (含自动2FA)
        console.log('示例5: 批量注册2个账户 (含自动2FA)');
        const batchTwoFAResults = await register.batchRegisterWithTwoFA(2, 20000, true, true);

        // 显示成功的账户
        const successfulAccounts = batchTwoFAResults.filter(result => result !== null);
        console.log(`\n成功注册的账户 (${successfulAccounts.length}个):`);
        successfulAccounts.forEach((account, index) => {
            console.log(`${index + 1}. 邮箱: ${account.email}`);
            console.log(`   密码: ${account.password}`);
            console.log(`   API Key: ${account.apiKey || '未获取到'}`);
            console.log(`   2FA启用: ${account.twoFactorEnabled}`);
            if (account.twoFactorInfo) {
                console.log(`   2FA Secret: ${account.twoFactorInfo.secret}`);
            }
            console.log('');
        });

    } catch (error) {
        console.error('批量2FA注册过程中发生错误:', error);
    }
}

// 主函数
async function main() {
    console.log('=== Targon.com 批量注册工具 - 完整示例 ===\n');

    // 根据命令行参数选择示例
    const example = process.argv[2] || 'basic';

    switch (example) {
        case 'basic':
            await basicExample();
            break;
        case '2fa':
            await twoFAExample();
            break;
        case 'batch2fa':
            await batchTwoFAExample();
            break;
        case 'all':
            await basicExample();
            await twoFAExample();
            await batchTwoFAExample();
            break;
        default:
            console.log('使用方法:');
            console.log('node example.js basic    - 基础注册示例');
            console.log('node example.js 2fa      - 2FA注册示例');
            console.log('node example.js batch2fa - 批量2FA注册示例');
            console.log('node example.js all      - 运行所有示例');
            break;
    }
}

// 运行示例
if (require.main === module) {
    main().catch(console.error);
}
