const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const TargonRegister = require('./targon-register');

class TargonWebServer {
    constructor() {
        this.app = express();
        this.port = 3000;
        this.register = new TargonRegister();
        this.isRegistering = false;
        this.shouldStop = false;
        
        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.static(__dirname));
    }

    setupRoutes() {
        // 主页
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'web-interface.html'));
        });

        // 开始注册
        this.app.post('/api/register', async (req, res) => {
            if (this.isRegistering) {
                return res.status(400).json({ error: '注册任务正在进行中' });
            }

            const { count, concurrency, mode } = req.body;
            
            if (!count || count < 1 || count > 50) {
                return res.status(400).json({ error: '注册数量必须在1-50之间' });
            }

            if (!concurrency || concurrency < 1 || concurrency > 10) {
                return res.status(400).json({ error: '并发数必须在1-10之间' });
            }

            this.isRegistering = true;
            this.shouldStop = false;

            // 设置流式响应
            res.writeHead(200, {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            });

            try {
                if (mode === 'parallel') {
                    await this.runParallelRegistration(count, concurrency, res);
                } else {
                    await this.runSerialRegistration(count, res);
                }
            } catch (error) {
                this.sendUpdate(res, {
                    type: 'log',
                    message: `❌ 注册过程出错: ${error.message}`
                });
            } finally {
                this.isRegistering = false;
                res.end();
            }
        });

        // 停止注册
        this.app.post('/api/stop', (req, res) => {
            this.shouldStop = true;
            res.json({ message: '已请求停止注册' });
        });

        // 获取账户列表
        this.app.get('/api/accounts', async (req, res) => {
            try {
                const accounts = await this.loadAccounts();
                res.json(accounts);
            } catch (error) {
                res.status(500).json({ error: '加载账户列表失败' });
            }
        });

        // 清空账户记录
        this.app.delete('/api/accounts', async (req, res) => {
            try {
                await fs.writeFile(this.register.outputFile, '[]', 'utf8');
                res.json({ message: '账户记录已清空' });
            } catch (error) {
                res.status(500).json({ error: '清空记录失败' });
            }
        });
    }

    async runParallelRegistration(count, concurrency, res) {
        this.sendUpdate(res, {
            type: 'log',
            message: `🚀 开始并行注册 ${count} 个账户，最大并发数: ${concurrency}`
        });

        const results = [];
        const promises = [];
        let completed = 0;

        // 创建注册任务
        for (let i = 1; i <= count; i++) {
            if (this.shouldStop) break;

            const promise = this.registerSingleAccountWithProgress(i, count, res);
            promises.push(promise);

            // 控制并发数
            if (promises.length >= concurrency || i === count) {
                const batchResults = await Promise.allSettled(promises);

                // 处理结果
                for (const result of batchResults) {
                    completed++;
                    
                    if (result.status === 'fulfilled' && result.value) {
                        results.push(result.value);
                        this.sendUpdate(res, {
                            type: 'account',
                            account: result.value
                        });
                    } else {
                        results.push(null);
                    }

                    this.sendUpdate(res, {
                        type: 'progress',
                        current: completed,
                        total: count
                    });
                }

                // 清空当前批次
                promises.length = 0;

                // 如果不是最后一批，稍微等待一下
                if (i < count && !this.shouldStop) {
                    await this.sleep(1000);
                }
            }
        }

        const successCount = results.filter(r => r !== null).length;
        this.sendUpdate(res, {
            type: 'complete',
            success: successCount,
            failed: count - successCount,
            total: count
        });
    }

    async runSerialRegistration(count, res) {
        this.sendUpdate(res, {
            type: 'log',
            message: `🚀 开始串行注册 ${count} 个账户`
        });

        const results = [];

        for (let i = 1; i <= count; i++) {
            if (this.shouldStop) break;

            const result = await this.registerSingleAccountWithProgress(i, count, res);
            results.push(result);

            if (result) {
                this.sendUpdate(res, {
                    type: 'account',
                    account: result
                });
            }

            this.sendUpdate(res, {
                type: 'progress',
                current: i,
                total: count
            });

            // 如果不是最后一个账户，等待一下
            if (i < count && !this.shouldStop) {
                await this.sleep(2000);
            }
        }

        const successCount = results.filter(r => r !== null).length;
        this.sendUpdate(res, {
            type: 'complete',
            success: successCount,
            failed: count - successCount,
            total: count
        });
    }

    async registerSingleAccountWithProgress(index, total, res) {
        try {
            this.sendUpdate(res, {
                type: 'log',
                message: `[${index}/${total}] 🚀 开始注册账户...`
            });

            const result = await this.register.registerSingleAccountWithIndex(index, total);

            if (result) {
                this.sendUpdate(res, {
                    type: 'log',
                    message: `[${index}/${total}] 🎉 注册成功! API: ${result.apiKey?.slice(-8) || 'N/A'} | 2FA: ${result.twoFactorEnabled ? '✅' : '❌'}`
                });
            } else {
                this.sendUpdate(res, {
                    type: 'log',
                    message: `[${index}/${total}] ❌ 注册失败`
                });
            }

            return result;
        } catch (error) {
            this.sendUpdate(res, {
                type: 'log',
                message: `[${index}/${total}] ❌ 注册失败: ${error.message}`
            });
            return null;
        }
    }

    sendUpdate(res, data) {
        try {
            res.write(JSON.stringify(data) + '\n');
        } catch (error) {
            console.error('发送更新失败:', error);
        }
    }

    async loadAccounts() {
        try {
            const fileContent = await fs.readFile(this.register.outputFile, 'utf8');
            return JSON.parse(fileContent);
        } catch (error) {
            return [];
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    start() {
        this.app.listen(this.port, () => {
            console.log(`🌐 Targon Web Interface 已启动`);
            console.log(`📱 访问地址: http://localhost:${this.port}`);
            console.log(`🚀 准备开始批量注册 Targon 账户！`);
        });
    }
}

// 启动服务器
if (require.main === module) {
    const server = new TargonWebServer();
    server.start();
}

module.exports = TargonWebServer;
