const TargonRegister = require('./targon-register');

async function test2FA() {
    const register = new TargonRegister();
    
    console.log('🧪 开始测试2FA功能...');
    
    try {
        // 注册一个账户并测试2FA
        const result = await register.registerSingleAccount();
        
        if (result) {
            console.log('\n📊 注册结果:');
            console.log('Email:', result.email);
            console.log('Password:', result.password);
            console.log('API Key:', result.apiKey);
            console.log('2FA Enabled:', result.twoFactorEnabled);
            
            if (result.twoFactorData) {
                console.log('\n🔐 2FA信息:');
                console.log('Manual Code:', result.twoFactorData.manualCode);
                console.log('Two Factor Secret:', result.twoFactorData.twoFactorSecret);
            }
            
            if (result.creditBalance) {
                console.log('\n💰 余额信息:');
                console.log('Bought Credits:', result.creditBalance.boughtCredits);
                console.log('Plan Credits:', result.creditBalance.planCredits);
                console.log('Total Credits:', result.creditBalance.totalCredits);
            }
            
            console.log('\n✅ 测试完成！');
        } else {
            console.log('❌ 注册失败');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    test2FA().catch(console.error);
}

module.exports = test2FA;
